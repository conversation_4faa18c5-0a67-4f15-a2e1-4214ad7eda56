# AI诊断功能配置指南

## 功能概述

AI诊断功能可以根据患者的最新医疗数据，生成专业的诊断建议和治疗方案。系统支持以下高级功能：

### 核心功能
- **智能数据收集**: 自动收集患者的完整医疗数据
- **Markdown渲染**: 支持AI返回的markdown格式内容，包括标题、列表、表格、代码块等
- **联网查询**: AI可以搜索最新的医学文献和资料
- **深度思考**: 显示AI的思考过程，提高诊断透明度

### 数据来源
- 最新病情记录
- 最近肿瘤指标（所有记录）
- 最新血常规指标
- 最新生化指标
- 最新凝血指标
- 最近CT报告（所有记录）

## 配置步骤

### 1. 打开AI配置

在健康资讯页面，切换到"AI诊断"轮播页面，点击"AI配置"按钮。

### 2. 填写配置信息

#### API地址
- **OpenAI官方**: `https://api.openai.com/v1/chat/completions`
- **国内代理**: 根据您使用的代理服务商提供的地址
- **自部署**: 您自己部署的AI服务地址

#### API密钥
- 从您的AI服务提供商获取的API密钥
- 请确保密钥有足够的权限和余额

#### 模型名称
- **OpenAI**: `gpt-3.5-turbo`, `gpt-4`, `gpt-4-turbo`
- **其他**: 根据您使用的服务商支持的模型

#### 温度参数
- 范围: 0.1 - 1.0
- 推荐: 0.7 (平衡创造性和准确性)
- 较低值(0.1-0.3): 更保守、一致的回答
- 较高值(0.7-1.0): 更有创造性的回答

### 3. 保存配置

点击"确定"保存配置，配置信息会保存在本地浏览器中。

## 使用方法

1. 确保已配置AI API信息
2. 在AI诊断页面点击"开始AI诊断"
3. 系统会显示加载状态：
   - "AI正在收集数据..." - 从数据库获取医疗数据
   - "AI正在深度思考..." - AI分析和推理过程
   - "AI正在整理结果..." - 格式化输出结果
4. 查看诊断结果：
   - **🧠 AI思考过程**: 点击展开查看AI的分析思路（如果支持）
   - **🔍 联网查询结果**: 查看AI搜索的相关医学资料（如果支持）
   - **主要诊断内容**: 完整的诊断建议，支持markdown格式显示

## 提示词模板

系统会自动生成如下格式的提示词：

```
假设你是一位专业的肿瘤科医生，病人是胰腺癌患者，

最新病情记录（日期）：
记录名称：...
病情信息：...
患者状态：...

最近肿瘤指标：
CA19-9：... U/ml
CEA：... ng/ml
...

最新血常规：
白细胞计数：... ×10^9/L
红细胞计数：... ×10^12/L
...

最新生化指标：
总胆红素：... μmol/L
直接胆红素：... μmol/L
...

最新凝血指标：
PT：... s
APTT：... s
...

最近CT报告：
第1次CT报告（日期）：
检查信息：...
诊断结果：...

请根据以上信息，作为专业肿瘤科医生，提供：
1. 当前病情分析
2. 诊断意见
3. 后续治疗建议
4. 注意事项

请用专业但易懂的语言回答。
```

## 新功能特性

### Markdown渲染支持
AI返回的内容现在支持完整的markdown语法：
- **标题**: # ## ### 等不同级别标题
- **列表**: 有序列表和无序列表
- **表格**: 完整的表格显示
- **代码**: 行内代码和代码块，支持语法高亮
- **引用**: 块引用样式
- **强调**: **粗体** 和 *斜体* 文本
- **链接**: 可点击的链接

### 联网查询功能
当AI服务支持时，会显示：
- 搜索到的相关医学文献
- 文章标题和来源链接
- 内容摘要
- 可展开/收起查看详情

### 深度思考展示
当AI服务支持时，会显示：
- AI的分析思路和推理过程
- 逐步的诊断逻辑
- 考虑的各种可能性
- 可展开/收起查看详情

## 注意事项

1. **数据隐私**: AI诊断会将您的医疗数据发送到AI服务商，请确保您信任该服务商
2. **仅供参考**: AI诊断结果仅供参考，不能替代专业医生的诊断
3. **及时就医**: 如有紧急情况，请立即就医
4. **配置安全**: 请妥善保管您的API密钥，不要泄露给他人
5. **功能支持**: 联网查询和深度思考功能需要AI服务商支持，不同服务商可能有不同的实现方式

## 常见问题

### Q: API调用失败怎么办？
A: 请检查：
- API地址是否正确
- API密钥是否有效
- 网络连接是否正常
- 账户余额是否充足

### Q: 如何获取API密钥？
A: 
- OpenAI: 访问 https://platform.openai.com/api-keys
- 其他服务商: 查看对应服务商的文档

### Q: 支持哪些AI模型？
A: 支持所有兼容OpenAI API格式的模型，包括：
- OpenAI GPT系列
- Claude (通过代理)
- 国产大模型 (如通义千问、文心一言等，需要相应的API接口)

### Q: 数据会被保存吗？
A: 
- 本应用不会保存您的医疗数据
- AI服务商可能会根据其政策保存对话记录
- 建议查看您使用的AI服务商的隐私政策
