# OpenRouter DeepSeek模型配置指南

## 配置信息

### API配置
- **API地址**: `https://openrouter.ai/api/v1/chat/completions`
- **API密钥**: 从OpenRouter获取的API密钥
- **模型名称**: `deepseek/deepseek-chat` 或 `deepseek/deepseek-reasoner`
- **温度参数**: `0.7` (推荐值)

### DeepSeek模型特性

#### deepseek-chat
- 通用对话模型
- 支持多轮对话
- 适合一般医疗咨询

#### deepseek-reasoner
- 具备深度推理能力
- 支持复杂逻辑分析
- **推荐用于医疗诊断**
- 会显示详细的思考过程

## 流式显示功能

### 支持的功能
1. **深度思考过程**: 实时显示AI的推理过程
2. **联网搜索**: 搜索最新医学文献和资料
3. **流式输出**: 逐字显示诊断结果
4. **搜索状态**: 显示搜索进度和状态

### 显示效果
```
🧠 AI深度思考过程 [思考中...] [展开]
├── 实时显示AI的分析思路
├── 逐步推理过程
└── 光标闪烁效果

🔍 联网查询结果 (3条) [展开]
├── 相关医学文献标题
├── 来源链接（可点击）
└── 内容摘要

主要诊断内容
├── Markdown格式显示
├── 流式逐字输出
└── 光标闪烁效果
```

## 配置步骤

### 1. 获取OpenRouter API密钥
1. 访问 https://openrouter.ai/
2. 注册账号并登录
3. 前往 API Keys 页面
4. 创建新的API密钥
5. 复制密钥备用

### 2. 在应用中配置
1. 打开健康资讯页面
2. 切换到"AI诊断"轮播页面
3. 点击"AI配置"按钮
4. 填写配置信息：
   - API地址: `https://openrouter.ai/api/v1/chat/completions`
   - API密钥: 您的OpenRouter API密钥
   - 模型名称: `deepseek/deepseek-reasoner`
   - 温度参数: `0.7`
5. 点击确定保存

### 3. 测试配置
1. 点击"开始AI诊断"
2. 观察加载状态变化
3. 查看思考过程是否显示
4. 确认搜索功能是否工作
5. 验证流式输出效果

## 特殊功能说明

### 深度思考过程
DeepSeek Reasoner模型会在生成回答前进行深度思考，这个过程会实时显示：
- 分析患者的基因检测结果
- 结合病理报告进行推理
- 考虑各种治疗方案
- 评估风险和预后

### 联网搜索功能
模型可以搜索最新的医学资料：
- PubMed医学文献
- 最新治疗指南
- 临床试验信息
- 药物相互作用数据

### 流式显示效果
- **思考过程**: 实时显示，带闪烁光标
- **搜索状态**: 显示搜索进度
- **主要内容**: 逐字输出，支持markdown

## 提示词优化

系统已针对胰腺癌患者优化了提示词，包含：
- 基因检测报告详情
- 病理报告和免疫组化结果
- 最新病情记录
- 各项医学指标
- CT报告信息

## 注意事项

### 费用说明
- OpenRouter按token计费
- DeepSeek模型相对便宜
- 深度思考会消耗更多token
- 联网搜索可能产生额外费用

### 使用建议
1. **首次使用**: 建议使用deepseek-reasoner体验完整功能
2. **日常咨询**: 可使用deepseek-chat节省费用
3. **重要诊断**: 推荐使用reasoner模型获得详细分析
4. **网络环境**: 确保网络稳定以获得最佳流式体验

### 隐私保护
- 医疗数据会发送到OpenRouter和DeepSeek
- 请确保您同意相关隐私政策
- 建议不要包含过于敏感的个人信息
- 诊断结果仅供参考，不替代专业医疗建议

## 故障排除

### 常见问题
1. **API调用失败**
   - 检查API密钥是否正确
   - 确认账户余额充足
   - 验证网络连接

2. **思考过程不显示**
   - 确认使用deepseek-reasoner模型
   - 检查模型是否支持reasoning功能
   - 尝试重新配置

3. **搜索功能不工作**
   - 确认模型支持联网搜索
   - 检查API参数配置
   - 验证网络环境

4. **流式显示异常**
   - 检查浏览器兼容性
   - 确认网络稳定性
   - 尝试刷新页面

### 技术支持
- OpenRouter官方文档: https://openrouter.ai/docs
- DeepSeek模型说明: https://openrouter.ai/models/deepseek
- 社区支持: OpenRouter Discord社区

## 更新日志

### v1.0 (当前版本)
- 支持DeepSeek Reasoner模型
- 实现流式思考过程显示
- 添加联网搜索结果展示
- 优化markdown渲染效果
- 改进用户交互体验
