<template>
  <div class="news-container">
    <!-- 动态背景元素 -->
    <div class="bg-animation">
      <div class="floating-cross" v-for="n in 8" :key="n" :style="{animationDelay: n * 0.6 + 's'}">+</div>
      <div class="floating-circle" v-for="n in 6" :key="n" :style="{animationDelay: n * 0.8 + 's'}"></div>
      <div class="floating-heart" v-for="n in 4" :key="n" :style="{animationDelay: n * 1.2 + 's'}">♥</div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-section">
      <div class="content-card">
        <h1 class="page-title">健康资讯</h1>

        <!-- 轮播容器 -->
        <div class="swipe-container">
          <!-- 左箭头 -->
          <div class="swipe-arrow swipe-arrow-left" @click="prevSlide">
            <span class="arrow-icon">‹</span>
          </div>

          <!-- 右箭头 -->
          <div class="swipe-arrow swipe-arrow-right" @click="nextSlide">
            <span class="arrow-icon">›</span>
          </div>

          <van-swipe
            ref="swipeRef"
            :autoplay="0"
            :show-indicators="false"
            class="medical-swipe"
            @change="onSwipeChange"
          >
            <!-- AI诊断 -->
            <van-swipe-item>
              <div class="swipe-content">
                <div class="section-header">
                  <div class="section-title-wrapper">
                    <div class="section-icon">🤖</div>
                    <h3 class="section-title">AI诊断助手</h3>
                  </div>
                  <div class="section-subtitle">智能分析病情，提供诊疗建议</div>
                </div>
                <div class="ai-diagnosis-content">
                  <div class="ai-status" v-if="aiDiagnosisLoading">
                    <van-loading size="20px" />
                    <span>{{ aiLoadingText }}</span>
                  </div>
                  <div v-else-if="aiDiagnosisResult" class="ai-result">
                    <div class="result-header">
                      <div class="result-title">AI诊断结果</div>
                      <div class="result-time">{{ formatTime(aiDiagnosisResult.timestamp) }}</div>
                    </div>

                    <!-- 思考过程显示 -->
                    <div v-if="aiDiagnosisResult.thinking" class="thinking-section">
                      <div class="thinking-header" @click="showThinking = !showThinking">
                        <span class="thinking-title">🧠 AI思考过程</span>
                        <span class="thinking-toggle">{{ showThinking ? '收起' : '展开' }}</span>
                      </div>
                      <div v-show="showThinking" class="thinking-content">
                        <div class="markdown-content" v-html="renderMarkdown(aiDiagnosisResult.thinking)"></div>
                      </div>
                    </div>

                    <!-- 搜索结果显示 -->
                    <div v-if="aiDiagnosisResult.searchResults && aiDiagnosisResult.searchResults.length > 0" class="search-section">
                      <div class="search-header" @click="showSearchResults = !showSearchResults">
                        <span class="search-title">🔍 联网查询结果</span>
                        <span class="search-toggle">{{ showSearchResults ? '收起' : '展开' }}</span>
                      </div>
                      <div v-show="showSearchResults" class="search-content">
                        <div v-for="(result, index) in aiDiagnosisResult.searchResults" :key="index" class="search-item">
                          <div class="search-item-title">{{ result.title }}</div>
                          <div class="search-item-url">{{ result.url }}</div>
                          <div class="search-item-snippet">{{ result.snippet }}</div>
                        </div>
                      </div>
                    </div>

                    <!-- 主要诊断内容 -->
                    <div class="result-content">
                      <div class="markdown-content" v-html="renderMarkdown(aiDiagnosisResult.content)"></div>
                    </div>
                  </div>
                  <div v-else class="ai-prompt">
                    <div class="prompt-text">点击下方按钮，让AI分析您的病情</div>
                  </div>
                  <div class="ai-actions">
                    <van-button
                      type="primary"
                      size="large"
                      block
                      :loading="aiDiagnosisLoading"
                      @click="startAIDiagnosis"
                    >
                      {{ aiDiagnosisLoading ? '分析中...' : '开始AI诊断' }}
                    </van-button>
                    <van-button
                      type="default"
                      size="small"
                      block
                      @click="showAIConfig = true"
                      style="margin-top: 8px;"
                    >
                      AI配置
                    </van-button>
                  </div>
                </div>
              </div>
            </van-swipe-item>

            <!-- 护理指南 -->
            <van-swipe-item>
              <div class="swipe-content">
                <div class="section-header">
                  <div class="section-title-wrapper">
                    <div class="section-icon">👩‍⚕️</div>
                    <h3 class="section-title">护理指南</h3>
                  </div>
                  <div class="section-subtitle">专业护理建议</div>
                </div>
                <div class="guide-content">
                  <div class="guide-item">
                    <h4>胰腺癌患者日常护理</h4>
                    <p>保持良好的生活习惯，定期监测身体状况，注意营养补充和体重管理。</p>
                  </div>
                  <div class="guide-item">
                    <h4>饮食建议</h4>
                    <p>少食多餐，选择易消化的食物，避免高脂肪食物，适当补充酶制剂。</p>
                  </div>
                  <div class="guide-item">
                    <h4>运动指导</h4>
                    <p>根据身体状况进行适度运动，如散步、太极等，避免剧烈运动。</p>
                  </div>
                  <div class="guide-item">
                    <h4>心理支持</h4>
                    <p>保持积极心态，与家人朋友沟通，必要时寻求专业心理帮助。</p>
                  </div>
                </div>
              </div>
            </van-swipe-item>

            <!-- 临床试验 -->
            <van-swipe-item>
              <div class="swipe-content">
                <div class="section-header">
                  <div class="section-title-wrapper">
                    <div class="section-icon">🔬</div>
                    <h3 class="section-title">临床试验</h3>
                  </div>
                  <div class="section-subtitle">最新试验信息</div>
                </div>
                <div class="trials-content">
                  <div class="trials-header">
                    <h4>中国临床试验注册中心</h4>
                    <p>查询最新的胰腺癌相关临床试验信息</p>
                  </div>
                  <div class="trials-actions">
                    <van-button
                      type="primary"
                      icon="external-link-o"
                      size="large"
                      block
                      @click="openTrialsWebsite"
                    >
                      访问临床试验中心
                    </van-button>
                  </div>
                  <div class="trials-info">
                    <h5>您可以在这里：</h5>
                    <ul>
                      <li>查询最新的胰腺癌相关临床试验</li>
                      <li>了解试验招募条件和联系方式</li>
                      <li>获取试验进展和结果信息</li>
                      <li>联系试验负责人获取更多详情</li>
                    </ul>
                  </div>
                </div>
              </div>
            </van-swipe-item>
          </van-swipe>

          <!-- 自定义指示器 -->
          <div class="custom-indicators">
            <div
              v-for="(item, index) in slideItems"
              :key="index"
              class="indicator-dot"
              :class="{ active: currentSlide === index }"
              @click="goToSlide(index)"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- AI配置弹窗 -->
    <van-dialog
      v-model:show="showAIConfig"
      title="AI配置"
      show-cancel-button
      @confirm="saveAIConfig"
      @cancel="cancelAIConfig"
    >
      <div class="ai-config-form">
        <van-field
          v-model="aiConfig.apiUrl"
          label="API地址"
          placeholder="请输入AI API地址"
          required
        />
        <van-field
          v-model="aiConfig.apiKey"
          label="API密钥"
          placeholder="请输入API密钥"
          type="password"
          required
        />
        <van-field
          v-model="aiConfig.model"
          label="模型名称"
          placeholder="请输入模型名称"
          required
        />
        <van-field
          v-model="aiConfig.temperature"
          label="温度参数"
          placeholder="0.1-1.0"
          type="number"
        />
        <div class="config-note">
          <p>注意：请确保API地址和密钥的正确性</p>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { showToast } from 'vant';
import { useStore } from 'vuex';
import { marked } from 'marked';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css';
import {
  getMedicalRecordList,
  getMedicalIndexValue,
  getMedicalExamLatest
} from '@/api/medical';

const store = useStore();

// 轮播相关
const swipeRef = ref(null);
const currentSlide = ref(0);

// 轮播项目
const slideItems = computed(() => [
  { title: 'AI诊断', icon: '🤖' },
  { title: '护理指南', icon: '👩‍⚕️' },
  { title: '临床试验', icon: '🔬' }
]);

// AI诊断相关
const aiDiagnosisLoading = ref(false);
const aiDiagnosisResult = ref(null);
const showAIConfig = ref(false);
const aiLoadingText = ref('AI正在分析中...');
const showThinking = ref(false);
const showSearchResults = ref(false);

// AI配置
const aiConfig = ref({
  apiUrl: localStorage.getItem('ai_api_url') || '',
  apiKey: localStorage.getItem('ai_api_key') || '',
  model: localStorage.getItem('ai_model') || 'gpt-3.5-turbo',
  temperature: localStorage.getItem('ai_temperature') || '0.7'
});

// 用户ID
const userId = computed(() => store.getters.userId || 1);

// 轮播控制方法
const prevSlide = () => {
  if (swipeRef.value) {
    swipeRef.value.prev();
  }
};

const nextSlide = () => {
  if (swipeRef.value) {
    swipeRef.value.next();
  }
};

const goToSlide = (index) => {
  if (swipeRef.value) {
    swipeRef.value.swipeTo(index);
  }
};

const onSwipeChange = (index) => {
  currentSlide.value = index;
};

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 配置marked
marked.setOptions({
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value;
      } catch (err) {}
    }
    return hljs.highlightAuto(code).value;
  },
  breaks: true,
  gfm: true
});

// 渲染markdown
const renderMarkdown = (content) => {
  if (!content) return '';
  return marked(content);
};

// 获取最新病情记录
const getLatestMedicalRecord = async () => {
  try {
    const response = await getMedicalRecordList({ page: 1, pageSize: 1 });
    if (response.data && response.data.data && response.data.data.length > 0) {
      return response.data.data[0];
    }
    return null;
  } catch (error) {
    console.error('获取病情记录失败:', error);
    return null;
  }
};

// 获取最近两次肿瘤指标
const getLatestTumorMarkers = async () => {
  try {
    const response = await getMedicalIndexValue({
      medical_type: 4,
      limit: 10
    });
    if (response.data && response.data.data) {
      return response.data.data;
    }
    return [];
  } catch (error) {
    console.error('获取肿瘤指标失败:', error);
    return [];
  }
};

// 获取最新血常规
const getLatestBloodTest = async () => {
  try {
    const response = await getMedicalIndexValue({
      medical_type: 2,
      limit: 10
    });
    if (response.data && response.data.data) {
      return response.data.data;
    }
    return [];
  } catch (error) {
    console.error('获取血常规失败:', error);
    return [];
  }
};

// 获取最新生化指标
const getLatestBiochemistry = async () => {
  try {
    const response = await getMedicalIndexValue({
      medical_type: 3,
      limit: 10
    });
    if (response.data && response.data.data) {
      return response.data.data;
    }
    return [];
  } catch (error) {
    console.error('获取生化指标失败:', error);
    return [];
  }
};

// 获取最新凝血指标
const getLatestCoagulation = async () => {
  try {
    const response = await getMedicalIndexValue({
      medical_type: 5,
      limit: 10
    });
    if (response.data && response.data.data) {
      return response.data.data;
    }
    return [];
  } catch (error) {
    console.error('获取凝血指标失败:', error);
    return [];
  }
};

// 获取最近两次CT报告
const getLatestCTReports = async () => {
  try {
    const response = await getMedicalExamLatest();
    if (response.data && response.data.data) {
      return response.data.data;
    }
    return [];
  } catch (error) {
    console.error('获取CT报告失败:', error);
    return [];
  }
};

// 生成AI诊断提示词
const generateAIPrompt = async () => {
  try {
    const [
      medicalRecord,
      tumorMarkers,
      bloodTest,
      biochemistry,
      coagulation,
      ctReports
    ] = await Promise.all([
      getLatestMedicalRecord(),
      getLatestTumorMarkers(),
      getLatestBloodTest(),
      getLatestBiochemistry(),
      getLatestCoagulation(),
      getLatestCTReports()
    ]);

    let prompt = "假设你是一位专业的肿瘤科医生，病人是胰腺癌患者，基因检测报告如下：\n";
    prompt += "1.肿瘤突变负荷(TMB):1.99个突变/Mb。2.对245个微卫星位点(MS)评估，其中82个位点可分析," +
      "该样本状态为:微卫星稳定, 3.在受检者石蜡组织DNA中检测到具有临床意义(I类)/潜在临床意义(II类)变异如下," +
      "(1)KRAS(NM033360.3)第2外显子错义突变c.35G>A(p.Gly12Asp)，AF22.18%," +
      "(2)CDK4基因拷贝数增高，拷贝数为8.7," +
      "(3)MDM2基因拷贝数增高，拷贝数为10.8," +
      "(4)GLI1基因拷贝数增高，拷贝数为6.7," +
      "(5)TP53(NM000546.5)第8外显子错义突变c.796G>A(p.Gly266Arg)，AF38.24%," +
      "(6)KDM6A(NM001291415.1)第12外显子无义突变c.1177C>T(p.Arg393*)，AF26.86% \n";
    prompt += "病人的穿刺病理报告和免疫组化结果如下：" +
      "(肝脏结节，穿刺活检)中一低分化腺癌，具有胰胆管型，结合病史首先考虑为胰腺来源。\n" +
      "免疫组化结果(HI25-16929) \n" +
      "瘤细胞：MLH1(+)，MSH6(+)，MSH2(+)，PMS2(+)，HER2(O)，SMAD4(+)，CDX-2(+)";
    // 添加最新病情记录
    if (medicalRecord) {
      prompt += `\n\n最新病情记录(${medicalRecord.record_date})：\n`;
      prompt += `记录名称：${medicalRecord.record_name}\n`;
      prompt += `病情信息：${medicalRecord.record_info}\n`;
      prompt += `患者状态：${medicalRecord.patient_status}\n`;
      if (medicalRecord.comment) {
        prompt += `备注：${medicalRecord.comment}\n`;
      }
    }

    // 添加最近两次肿瘤指标
    if (tumorMarkers.length > 0) {
      prompt += `\n\n最近肿瘤指标：\n`;
      tumorMarkers.slice(0, 10).forEach(marker => {
        prompt += `${marker.index_name}：${marker.index_value} ${marker.index_unit || ''}\n`;
      });
    }

    // 添加最新血常规
    if (bloodTest.length > 0) {
      prompt += `\n\n最新血常规：\n`;
      bloodTest.slice(0, 10).forEach(test => {
        prompt += `${test.index_name}：${test.index_value} ${test.index_unit || ''}\n`;
      });
    }

    // 添加最新生化指标
    if (biochemistry.length > 0) {
      prompt += `\n\n最新生化指标：\n`;
      biochemistry.slice(0, 10).forEach(bio => {
        prompt += `${bio.index_name}：${bio.index_value} ${bio.index_unit || ''}\n`;
      });
    }

    // 添加最新凝血指标
    if (coagulation.length > 0) {
      prompt += `\n\n最新凝血指标：\n`;
      coagulation.slice(0, 10).forEach(coag => {
        prompt += `${coag.index_name}：${coag.index_value} ${coag.index_unit || ''}\n`;
      });
    }

    // 添加最近两次CT报告
    if (ctReports.length > 0) {
      prompt += `\n\n最近CT报告：\n`;
      ctReports.forEach((report, index) => {
        prompt += `第${index + 1}次CT报告(${report.medical_date})：\n`;
        if (report.exam_info) {
          prompt += `检查信息：${report.exam_info}\n`;
        }
        if (report.exam_diag) {
          prompt += `诊断结果：${report.exam_diag}\n`;
        }
        prompt += `\n`;
      });
    }

    prompt += `\n\n请根据以上信息，作为专业肿瘤科医生，提供：\n1. 当前病情分析\n2. 诊断意见\n3. 后续治疗建议\n4. 注意事项\n\n请用专业但易懂的语言回答。`;

    return prompt;
  } catch (error) {
    console.error('生成提示词失败:', error);
    return "假设你是一位专业的肿瘤科医生，病人是胰腺癌患者，请提供一般性的诊疗建议。";
  }
};

// 调用AI API
const callAIAPI = async (prompt) => {
  if (!aiConfig.value.apiUrl || !aiConfig.value.apiKey) {
    throw new Error('请先配置AI API信息');
  }

  // 构建请求体，支持联网查询和深度思考
  const requestBody = {
    model: aiConfig.value.model,
    messages: [
      {
        role: 'user',
        content: prompt
      }
    ],
    temperature: parseFloat(aiConfig.value.temperature),
    max_tokens: 4000,
    // 支持联网查询
    web_search: true,
    // 支持深度思考
    thinking: true,
    stream: true
  };

  const response = await fetch(aiConfig.value.apiUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${aiConfig.value.apiKey}`
    },
    body: JSON.stringify(requestBody)
  });

  if (!response.ok) {
    throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();

  // 处理不同的响应格式
  if (data.choices && data.choices.length > 0) {
    const choice = data.choices[0];
    const result = {
      content: choice.message.content,
      thinking: null,
      searchResults: []
    };

    // 提取思考过程
    if (choice.message.thinking) {
      result.thinking = choice.message.thinking;
    }

    // 提取搜索结果
    if (data.web_search_results) {
      result.searchResults = data.web_search_results;
    }

    return result;
  } else {
    throw new Error('AI响应格式错误');
  }
};

// 开始AI诊断
const startAIDiagnosis = async () => {
  if (!aiConfig.value.apiUrl || !aiConfig.value.apiKey) {
    showToast('请先配置AI API信息');
    showAIConfig.value = true;
    return;
  }

  try {
    aiDiagnosisLoading.value = true;
    aiLoadingText.value = 'AI正在收集数据...';

    // 生成提示词
    const prompt = await generateAIPrompt();

    aiLoadingText.value = 'AI正在深度思考...';

    // 调用AI API
    const result = await callAIAPI(prompt);

    aiLoadingText.value = 'AI正在整理结果...';

    // 保存结果
    aiDiagnosisResult.value = {
      content: typeof result === 'string' ? result : result.content,
      thinking: result.thinking || null,
      searchResults: result.searchResults || [],
      timestamp: Date.now()
    };

    // 重置展开状态
    showThinking.value = false;
    showSearchResults.value = false;

    showToast('AI诊断完成');
  } catch (error) {
    console.error('AI诊断失败:', error);
    showToast(error.message || 'AI诊断失败，请检查配置');
  } finally {
    aiDiagnosisLoading.value = false;
    aiLoadingText.value = 'AI正在分析中...';
  }
};

// 保存AI配置
const saveAIConfig = () => {
  if (!aiConfig.value.apiUrl || !aiConfig.value.apiKey || !aiConfig.value.model) {
    showToast('请填写完整的配置信息');
    return;
  }

  localStorage.setItem('ai_api_url', aiConfig.value.apiUrl);
  localStorage.setItem('ai_api_key', aiConfig.value.apiKey);
  localStorage.setItem('ai_model', aiConfig.value.model);
  localStorage.setItem('ai_temperature', aiConfig.value.temperature);

  showAIConfig.value = false;
  showToast('配置保存成功');
};

// 取消AI配置
const cancelAIConfig = () => {
  // 恢复原始配置
  aiConfig.value = {
    apiUrl: localStorage.getItem('ai_api_url') || '',
    apiKey: localStorage.getItem('ai_api_key') || '',
    model: localStorage.getItem('ai_model') || 'gpt-3.5-turbo',
    temperature: localStorage.getItem('ai_temperature') || '0.7'
  };
  showAIConfig.value = false;
};

// 打开临床试验网站
const openTrialsWebsite = () => {
  window.open('https://www.chictr.org.cn/searchproj.html?title=%E8%83%B0%E8%85%BA%E7%99%8C&officialname=&subjectid=&regstatus=&regno=&secondaryid=&applier=&studyleader=&createyear=&sponsor=&secsponsor=&sourceofspends=&studyailment=&studyailmentcode=&studytype=1&studystage=&studydesign=&recruitmentstatus=&gender=&agreetosign=&measure=&country=&province=&city=&institution=&institutionlevel=&intercode=&ethicalcommitteesanction=&whetherpublic=&minstudyexecutetime=&maxstudyexecutetime=&btngo=btn', '_blank');
};

onMounted(() => {
  // 初始化时不需要加载数据，按需加载
});
</script>

<style scoped>
.news-container {
  min-height: calc(100vh - 50px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow-x: hidden;
  padding-bottom: 80px;
  box-sizing: border-box;
}

/* 动态背景元素样式 */
.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-cross {
  position: absolute;
  color: rgba(255, 255, 255, 0.08);
  font-size: 20px;
  font-weight: bold;
  animation: float 8s ease-in-out infinite;
}

.floating-cross:nth-child(1) { top: 5%; left: 8%; }
.floating-cross:nth-child(2) { top: 15%; right: 12%; }
.floating-cross:nth-child(3) { top: 45%; left: 3%; }
.floating-cross:nth-child(4) { top: 65%; right: 8%; }
.floating-cross:nth-child(5) { top: 25%; left: 85%; }
.floating-cross:nth-child(6) { bottom: 35%; left: 15%; }
.floating-cross:nth-child(7) { bottom: 15%; right: 20%; }
.floating-cross:nth-child(8) { top: 80%; left: 70%; }

.floating-circle {
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  animation: pulse 6s ease-in-out infinite;
}

.floating-circle:nth-child(9) { top: 12%; left: 50%; }
.floating-circle:nth-child(10) { top: 55%; right: 25%; }
.floating-circle:nth-child(11) { bottom: 25%; left: 60%; }
.floating-circle:nth-child(12) { top: 35%; right: 5%; }
.floating-circle:nth-child(13) { bottom: 45%; left: 35%; }
.floating-circle:nth-child(14) { top: 75%; right: 45%; }

.floating-heart {
  position: absolute;
  color: rgba(255, 255, 255, 0.06);
  font-size: 18px;
  animation: heartbeat 4s ease-in-out infinite;
}

.floating-heart:nth-child(15) { top: 30%; left: 20%; }
.floating-heart:nth-child(16) { top: 60%; right: 15%; }
.floating-heart:nth-child(17) { bottom: 20%; left: 80%; }
.floating-heart:nth-child(18) { top: 85%; left: 40%; }

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(180deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.08; }
  50% { transform: scale(1.3); opacity: 0.15; }
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.1); }
  50% { transform: scale(1); }
  75% { transform: scale(1.05); }
}

/* 内容区域 */
.content-section {
  position: relative;
  z-index: 2;
  /* margin-top: 20px; */
}

.content-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.page-title {
  color: #2c5aa0;
  font-size: 20px;
  font-weight: 600;
  margin: 8px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  color: #6b7280;
  font-size: 14px;
  margin: 10px 0 20px 0;
}

/* 轮播容器样式 */
.swipe-container {
  position: relative;
  margin-top: 10px;
}

.swipe-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.swipe-arrow:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-50%) scale(1.1);
}

.swipe-arrow-left {
  left: -16px;
}

.swipe-arrow-right {
  right: -16px;
}

.arrow-icon {
  font-size: 18px;
  color: #2c5aa0;
  font-weight: bold;
}

.medical-swipe {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.swipe-content {
  background: white;
  padding: 10px;
  min-height: 500px;
  text-align: left;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;
}

.section-title-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-icon {
  font-size: 24px;
}

.section-title {
  color: #2c5aa0;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.section-subtitle {
  color: #6b7280;
  font-size: 12px;
  margin: 0;
}

/* 自定义指示器 */
.custom-indicators {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 16px;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(44, 90, 160, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator-dot.active {
  background: #2c5aa0;
  transform: scale(1.2);
}

/* AI诊断样式 */
.ai-diagnosis-content {
  min-height: 450px;
  display: flex;
  flex-direction: column;
}

/* Markdown内容样式 */
.markdown-content {
  line-height: 1.6;
  color: #374151;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  color: #2c5aa0;
  margin: 16px 0 8px 0;
  font-weight: 600;
}

.markdown-content h1 { font-size: 18px; }
.markdown-content h2 { font-size: 16px; }
.markdown-content h3 { font-size: 15px; }
.markdown-content h4 { font-size: 14px; }
.markdown-content h5 { font-size: 13px; }
.markdown-content h6 { font-size: 12px; }

.markdown-content p {
  margin: 8px 0;
  font-size: 14px;
}

.markdown-content ul,
.markdown-content ol {
  margin: 8px 0;
  padding-left: 20px;
}

.markdown-content li {
  margin: 4px 0;
  font-size: 14px;
}

.markdown-content blockquote {
  border-left: 4px solid #2c5aa0;
  margin: 12px 0;
  padding: 8px 12px;
  background: #f8fafc;
  color: #6b7280;
  font-style: italic;
}

.markdown-content code {
  background: #f1f5f9;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #e11d48;
}

.markdown-content pre {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
  overflow-x: auto;
  margin: 12px 0;
}

.markdown-content pre code {
  background: none;
  padding: 0;
  color: inherit;
  font-size: 13px;
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 12px 0;
  font-size: 13px;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #e2e8f0;
  padding: 8px 12px;
  text-align: left;
}

.markdown-content th {
  background: #f8fafc;
  font-weight: 600;
  color: #2c5aa0;
}

.markdown-content strong {
  font-weight: 600;
  color: #2c5aa0;
}

.markdown-content em {
  font-style: italic;
  color: #6b7280;
}

.ai-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #6b7280;
  font-size: 14px;
  margin: 40px 0;
}

.ai-result {
  flex: 1;
  margin-bottom: 20px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.result-title {
  color: #2c5aa0;
  font-size: 16px;
  font-weight: 600;
}

.result-time {
  color: #6b7280;
  font-size: 12px;
}

.result-content {
  color: #374151;
  font-size: 14px;
  line-height: 1.6;
}

.ai-prompt {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  margin: 40px 0;
}

.prompt-text {
  color: #6b7280;
  font-size: 14px;
  text-align: center;
}

.ai-actions {
  margin-top: auto;
}

/* 思考过程样式 */
.thinking-section {
  margin-bottom: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.thinking-header {
  background: #f8fafc;
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e2e8f0;
  transition: background-color 0.2s;
}

.thinking-header:hover {
  background: #f1f5f9;
}

.thinking-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c5aa0;
}

.thinking-toggle {
  font-size: 12px;
  color: #6b7280;
}

.thinking-content {
  padding: 16px;
  background: #fefefe;
  max-height: 300px;
  overflow-y: auto;
}

/* 搜索结果样式 */
.search-section {
  margin-bottom: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.search-header {
  background: #f0f9ff;
  padding: 12px 16px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e2e8f0;
  transition: background-color 0.2s;
}

.search-header:hover {
  background: #e0f2fe;
}

.search-title {
  font-size: 14px;
  font-weight: 600;
  color: #0369a1;
}

.search-toggle {
  font-size: 12px;
  color: #6b7280;
}

.search-content {
  padding: 16px;
  background: #fefefe;
  max-height: 300px;
  overflow-y: auto;
}

.search-item {
  margin-bottom: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
  border-left: 3px solid #0369a1;
}

.search-item:last-child {
  margin-bottom: 0;
}

.search-item-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c5aa0;
  margin-bottom: 4px;
}

.search-item-url {
  font-size: 12px;
  color: #0369a1;
  margin-bottom: 6px;
  word-break: break-all;
}

.search-item-snippet {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
}

/* 护理指南样式 */
.guide-content {
  max-height: 470px;
  overflow-y: auto;
}

.guide-item {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #2c5aa0;
}

.guide-item h4 {
  color: #2c5aa0;
  font-size: 15px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.guide-item p {
  color: #374151;
  font-size: 13px;
  line-height: 1.5;
  margin: 0;
}

/* 临床试验样式 */
.trials-content {
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

.trials-header {
  text-align: center;
  margin-bottom: 20px;
}

.trials-header h4 {
  color: #2c5aa0;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.trials-header p {
  color: #6b7280;
  font-size: 13px;
  margin: 0;
}

.trials-actions {
  margin: 20px 0;
}

.trials-info {
  background: #f8fafc;
  padding: 15px;
  border-radius: 8px;
  margin-top: auto;
}

.trials-info h5 {
  color: #2c5aa0;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.trials-info ul {
  padding-left: 16px;
  margin: 0;
}

.trials-info li {
  color: #374151;
  font-size: 12px;
  margin-bottom: 6px;
  line-height: 1.4;
}

/* AI配置弹窗样式 */
.ai-config-form {
  padding: 20px 0;
}

.ai-config-form .van-field {
  margin-bottom: 16px;
}

.config-note {
  margin-top: 16px;
  padding: 12px;
  background: #fef3cd;
  border-radius: 6px;
  border-left: 4px solid #f59e0b;
}

.config-note p {
  color: #92400e;
  font-size: 12px;
  margin: 0;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .swipe-arrow {
    width: 28px;
    height: 28px;
  }

  .swipe-arrow-left {
    left: -14px;
  }

  .swipe-arrow-right {
    right: -14px;
  }

  .arrow-icon {
    font-size: 16px;
  }

  .section-title {
    font-size: 16px;
  }

  .section-icon {
    font-size: 20px;
  }
}
</style>





